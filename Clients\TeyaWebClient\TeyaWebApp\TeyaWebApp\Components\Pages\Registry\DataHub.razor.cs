using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using MudBlazor;
using TeyaUIViewModels.ViewModel;
using TeyaWebApp.Services;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using BusinessLayer.Services;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Sprache;
using TeyaWebApp.TeyaAIScribeResource;

namespace TeyaWebApp.Components.Pages.Registry
{
    public partial class DataHub : ComponentBase
    {
        [Inject] private ISnackbar? Snackbar { get; set; }
        [Inject] private ILogger<DataHub>? Logger { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;

        // Simple properties for testing
        private bool IsLoading = true;
        private bool HasError = false;
        private string ErrorMessage = string.Empty;
        private bool ButtonClicked = false;

        private void RefreshPage()
        {
            NavigationManager.Refresh(true);
        }

        private void TestAction()
        {
            ButtonClicked = true;
            StateHasChanged();
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                StateHasChanged();

                // Simple initialization delay
                await Task.Delay(500);

                // Initialization successful
                IsLoading = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error initializing DataHub: {ex.Message}");
                HasError = true;
                ErrorMessage = $"Error initializing DataHub: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }
    }
}
