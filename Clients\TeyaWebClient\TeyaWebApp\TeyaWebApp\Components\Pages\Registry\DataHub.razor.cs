using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.JSInterop;
using MudBlazor;
using TeyaWebApp.Models;
using TeyaWebApp.Services;
using TeyaUIModels.Model;
using TeyaUIModels.ViewModel;
using TeyaUIViewModels.ViewModel;
using BusinessLayer.Services;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace TeyaWebApp.Components.Pages.Registry
{
    public partial class DataHub : ComponentBase
    {
        [Inject] private IStringLocalizer<DataHub> Localizer { get; set; } = default!;
        [Inject] private IPatientService PatientService { get; set; } = default!;
        [Inject] private ISnackbar Snackbar { get; set; } = default!;
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;
        [Inject] private ILogger<DataHub> Logger { get; set; } = default!;
        [Inject] private ActiveUser ActiveUser { get; set; } = default!;
        [Inject] private IOrganizationService OrganizationService { get; set; } = default!;
        [Inject] private IJSRuntime JSRuntime { get; set; } = default!;

        // Filter Model
        private DataHubFilterModel FilterModel = new();

        // Results
        private List<PatientRegistryModel> PatientResults = new();
        private bool IsLoading = false;
        private bool SearchPerformed = false;
        private bool HasResults => PatientResults?.Any() == true;
        private int TotalPatients => PatientResults?.Count ?? 0;
        private int CurrentPage = 1;
        private int PageSize = 50;
        private int TotalPages => (int)Math.Ceiling((double)TotalPatients / PageSize);

        // Dialog States
        private bool ShowSaveQueryDialog = false;
        private bool ShowExportDialog = false;
        private bool ShowAdvancedFilters = false;
        private string QueryName = string.Empty;
        private string ExportFormat = "CSV";

        protected override async Task OnInitializedAsync()
        {
            try
            {
                // Initialize any default values or load saved queries
                await LoadSavedQueries();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error initializing DataHub: {ex.Message}");
                Snackbar.Add(Localizer["ErrorInitializing"], Severity.Error);
            }
        }

        private async Task SearchPatients()
        {
            try
            {
                IsLoading = true;
                SearchPerformed = true;
                StateHasChanged();

                // Build search criteria from filter model
                var searchCriteria = new PatientSearchCriteria
                {
                    AgeFrom = FilterModel.AgeFrom,
                    AgeTo = FilterModel.AgeTo,
                    Sex = FilterModel.Sex,
                    DOBFrom = FilterModel.DOBFrom,
                    DOBTo = FilterModel.DOBTo,
                    PCP = FilterModel.PCP,
                    Insurance = FilterModel.Insurance,
                    Language = FilterModel.Language,
                    OrganizationId = await GetCurrentOrganizationId(),

                    // Additional search criteria
                    PatientName = FilterModel.PatientName,
                    PhoneNumber = FilterModel.PhoneNumber,
                    AccountNumber = FilterModel.AccountNumber,
                    Address = FilterModel.Address,
                    City = FilterModel.City,
                    State = FilterModel.State,
                    ZipCode = FilterModel.ZipCode,
                    LastVisitFrom = FilterModel.LastVisitFrom,
                    LastVisitTo = FilterModel.LastVisitTo,
                    Diagnosis = FilterModel.Diagnosis,
                    Medication = FilterModel.Medication,
                    IsActive = FilterModel.IsActive,
                    RiskLevel = FilterModel.RiskLevel
                };

                // Get patients from service
                var patients = await PatientService.SearchPatientsAsync(searchCriteria);

                // Convert to registry model
                PatientResults = patients?.Select(p => new PatientRegistryModel
                {
                    Id = p.Id,
                    FullName = $"{p.FirstName} {p.LastName}",
                    DateOfBirth = p.DateOfBirth,
                    Sex = p.Sex,
                    Age = CalculateAge(p.DateOfBirth),
                    PhoneNumber = p.PhoneNumber,
                    AccountNumber = p.AccountNumber,

                    // Additional fields with sample data
                    Address = p.Address ?? "123 Main St",
                    City = p.City ?? "Sample City",
                    State = p.State ?? "CA",
                    ZipCode = p.ZipCode ?? "12345",
                    PCP = p.PCP ?? "Dr. Smith",
                    Insurance = p.Insurance ?? "Blue Cross",
                    Language = p.Language ?? "English",
                    LastVisit = p.LastVisit ?? DateTime.Now.AddDays(-30),
                    RiskLevel = p.RiskLevel ?? "Low",
                    IsActive = p.IsActive ?? true,
                    EmergencyContact = p.EmergencyContact ?? "Emergency Contact",
                    Email = p.Email ?? "<EMAIL>"
                }).ToList() ?? new List<PatientRegistryModel>();

                CurrentPage = 1;

                if (HasResults)
                {
                    Snackbar.Add(Localizer["SearchCompleted", TotalPatients], Severity.Success);
                }
                else
                {
                    Snackbar.Add(Localizer["NoResultsFound"], Severity.Info);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error searching patients: {ex.Message}");
                Snackbar.Add(Localizer["ErrorSearching"], Severity.Error);
                PatientResults = new List<PatientRegistryModel>();
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        private void ClearFilters()
        {
            FilterModel = new DataHubFilterModel();
            PatientResults = new List<PatientRegistryModel>();
            SearchPerformed = false;
            CurrentPage = 1;
            StateHasChanged();
            Snackbar.Add(Localizer["FiltersCleared"], Severity.Info);
        }

        private void SaveQuery()
        {
            QueryName = string.Empty;
            ShowSaveQueryDialog = true;
        }

        private async Task SaveQueryConfirm()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(QueryName))
                {
                    Snackbar.Add(Localizer["QueryNameRequired"], Severity.Warning);
                    return;
                }

                // Create saved query object
                var savedQuery = new SavedQuery
                {
                    Id = Guid.NewGuid(),
                    Name = QueryName,
                    UserId = ActiveUser.Id,
                    OrganizationId = await GetCurrentOrganizationId(),
                    CreatedDate = DateTime.Now,
                    FilterCriteria = System.Text.Json.JsonSerializer.Serialize(FilterModel)
                };

                // Save to local storage for now (in production, save to database)
                await SaveQueryToStorage(savedQuery);

                ShowSaveQueryDialog = false;
                Snackbar.Add(Localizer["QuerySaved", QueryName], Severity.Success);
                QueryName = string.Empty;

                // Reload saved queries
                await LoadSavedQueries();
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error saving query: {ex.Message}");
                Snackbar.Add(Localizer["ErrorSavingQuery"], Severity.Error);
            }
        }

        private async Task RunSubset()
        {
            try
            {
                // TODO: Implement subset functionality
                // This would run a predefined subset of patients based on specific criteria

                Snackbar.Add(Localizer["SubsetExecuted"], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error running subset: {ex.Message}");
                Snackbar.Add(Localizer["ErrorRunningSubset"], Severity.Error);
            }
        }

        private void ExportData()
        {
            if (!HasResults)
            {
                Snackbar.Add(Localizer["NoDataToExport"], Severity.Warning);
                return;
            }

            ExportFormat = "CSV";
            ShowExportDialog = true;
        }

        private async Task ExportConfirm()
        {
            try
            {
                ShowExportDialog = false;

                // TODO: Implement export functionality based on format
                switch (ExportFormat.ToUpper())
                {
                    case "CSV":
                        await ExportToCsv();
                        break;
                    case "EXCEL":
                        await ExportToExcel();
                        break;
                    case "PDF":
                        await ExportToPdf();
                        break;
                }

                Snackbar.Add(Localizer["ExportCompleted", ExportFormat], Severity.Success);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error exporting data: {ex.Message}");
                Snackbar.Add(Localizer["ErrorExporting"], Severity.Error);
            }
        }

        private async Task ViewPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient chart
                NavigationManager.NavigateTo($"/Chart?patientId={patientId}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error navigating to patient: {ex.Message}");
                Snackbar.Add(Localizer["ErrorViewingPatient"], Severity.Error);
            }
        }

        private async Task OnPageChanged(int page)
        {
            CurrentPage = page;
            StateHasChanged();
        }

        private void ToggleAdvancedFilters()
        {
            ShowAdvancedFilters = !ShowAdvancedFilters;
            StateHasChanged();
        }

        private Color GetRiskLevelColor(string riskLevel)
        {
            return riskLevel?.ToLower() switch
            {
                "low" => Color.Success,
                "medium" => Color.Warning,
                "high" => Color.Error,
                "critical" => Color.Error,
                _ => Color.Default
            };
        }

        private async Task EditPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient edit page
                NavigationManager.NavigateTo($"/Patient/Edit/{patientId}");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error navigating to edit patient: {ex.Message}");
                Snackbar.Add(Localizer["ErrorEditingPatient"], Severity.Error);
            }
        }

        private async Task<Guid> GetCurrentOrganizationId()
        {
            try
            {
                // Get organization ID from active user using the same pattern as other components
                if (!string.IsNullOrEmpty(ActiveUser.OrganizationName))
                {
                    return await OrganizationService.GetOrganizationIdByNameAsync(ActiveUser.OrganizationName);
                }
                return Guid.Empty;
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error getting organization ID: {ex.Message}");
                return Guid.Empty;
            }
        }

        private int CalculateAge(DateTime? dateOfBirth)
        {
            if (!dateOfBirth.HasValue) return 0;

            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;

            if (dateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age;
        }

        private async Task LoadSavedQueries()
        {
            try
            {
                // TODO: Implement loading saved queries
                // This would load previously saved query criteria for the user
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error loading saved queries: {ex.Message}");
            }
        }

        private async Task SaveQueryToStorage(SavedQuery query)
        {
            try
            {
                // For demo purposes, save to browser local storage
                // In production, this would save to database
                var json = System.Text.Json.JsonSerializer.Serialize(query);
                await JSRuntime.InvokeVoidAsync("localStorage.setItem", $"datahub_query_{query.Id}", json);
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error saving query to storage: {ex.Message}");
                throw;
            }
        }

        private async Task ExportToCsv()
        {
            try
            {
                var csv = new StringBuilder();

                // Add headers
                csv.AppendLine("Patient Name,Date of Birth,Sex,Age,Phone Number,Account Number");

                // Add data rows
                foreach (var patient in PatientResults)
                {
                    csv.AppendLine($"\"{patient.FullName}\",\"{patient.DateOfBirth?.ToString("MM/dd/yyyy")}\",\"{patient.Sex}\",{patient.Age},\"{patient.PhoneNumber}\",\"{patient.AccountNumber}\"");
                }

                // Create download
                var fileName = $"PatientRegistry_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
                var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
                var base64 = Convert.ToBase64String(bytes);

                await DownloadFile(fileName, base64, "text/csv");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error exporting to CSV: {ex.Message}");
                throw;
            }
        }

        private async Task ExportToExcel()
        {
            try
            {
                // Simple Excel export using CSV format with .xlsx extension
                // For full Excel support, would need EPPlus or similar library
                var csv = new StringBuilder();

                // Add headers
                csv.AppendLine("Patient Name,Date of Birth,Sex,Age,Phone Number,Account Number");

                // Add data rows
                foreach (var patient in PatientResults)
                {
                    csv.AppendLine($"\"{patient.FullName}\",\"{patient.DateOfBirth?.ToString("MM/dd/yyyy")}\",\"{patient.Sex}\",{patient.Age},\"{patient.PhoneNumber}\",\"{patient.AccountNumber}\"");
                }

                var fileName = $"PatientRegistry_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                var bytes = System.Text.Encoding.UTF8.GetBytes(csv.ToString());
                var base64 = Convert.ToBase64String(bytes);

                await DownloadFile(fileName, base64, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error exporting to Excel: {ex.Message}");
                throw;
            }
        }

        private async Task ExportToPdf()
        {
            try
            {
                // Simple PDF export using HTML to PDF conversion
                // For production, would use a proper PDF library like iTextSharp
                var html = GeneratePdfHtml();
                var fileName = $"PatientRegistry_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";

                // This would require a PDF generation service
                // For now, export as HTML
                var bytes = System.Text.Encoding.UTF8.GetBytes(html);
                var base64 = Convert.ToBase64String(bytes);

                await DownloadFile(fileName.Replace(".pdf", ".html"), base64, "text/html");
            }
            catch (Exception ex)
            {
                Logger.LogError($"Error exporting to PDF: {ex.Message}");
                throw;
            }
        }

        private async Task DownloadFile(string fileName, string base64Content, string contentType)
        {
            await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64Content, contentType);
        }

        private string GeneratePdfHtml()
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html><head><title>Patient Registry Report</title>");
            html.AppendLine("<style>body{font-family:Arial,sans-serif;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background-color:#f2f2f2;}</style>");
            html.AppendLine("</head><body>");
            html.AppendLine($"<h1>Patient Registry Report</h1>");
            html.AppendLine($"<p>Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine($"<p>Total Patients: {TotalPatients}</p>");
            html.AppendLine("<table>");
            html.AppendLine("<tr><th>Patient Name</th><th>Date of Birth</th><th>Sex</th><th>Age</th><th>Phone Number</th><th>Account Number</th></tr>");

            foreach (var patient in PatientResults)
            {
                html.AppendLine($"<tr><td>{patient.FullName}</td><td>{patient.DateOfBirth?.ToString("MM/dd/yyyy")}</td><td>{patient.Sex}</td><td>{patient.Age}</td><td>{patient.PhoneNumber}</td><td>{patient.AccountNumber}</td></tr>");
            }

            html.AppendLine("</table>");
            html.AppendLine("</body></html>");

            return html.ToString();
        }
    }


}
