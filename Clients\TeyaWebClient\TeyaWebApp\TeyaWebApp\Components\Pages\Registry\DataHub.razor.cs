using Microsoft.AspNetCore.Components;
using MudBlazor;

namespace TeyaWebApp.Components.Pages.Registry
{
    public partial class DataHub : ComponentBase
    {
        [Inject] private ISnackbar? Snackbar { get; set; }
        [Inject] private ILogger<DataHub>? Logger { get; set; }
        [Inject] private NavigationManager NavigationManager { get; set; } = default!;

        // Filter properties
        private int? AgeFrom { get; set; }
        private int? AgeTo { get; set; }
        private string Sex { get; set; } = string.Empty;
        private string PatientName { get; set; } = string.Empty;
        private string PhoneNumber { get; set; } = string.Empty;
        private string Insurance { get; set; } = string.Empty;

        // Results properties
        private List<PatientRegistryModel> PatientResults = new();
        private bool IsLoading = true;
        private bool HasError = false;
        private string ErrorMessage = string.Empty;
        private bool SearchPerformed = false;
        private bool HasResults => PatientResults?.Any() == true;
        private int TotalPatients => PatientResults?.Count ?? 0;

        private void RefreshPage()
        {
            NavigationManager.Refresh(true);
        }

        protected override async Task OnInitializedAsync()
        {
            try
            {
                IsLoading = true;
                HasError = false;
                StateHasChanged();

                // Simple initialization delay
                await Task.Delay(500);

                // Initialization successful
                IsLoading = false;
                StateHasChanged();
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error initializing DataHub: {ex.Message}");
                HasError = true;
                ErrorMessage = $"Error initializing DataHub: {ex.Message}";
                IsLoading = false;
                StateHasChanged();
            }
        }

        private async Task SearchPatients()
        {
            try
            {
                SearchPerformed = true;
                StateHasChanged();

                // Simulate search with sample data
                await Task.Delay(1000); // Simulate API call

                // Generate sample patient data based on filters
                PatientResults = GenerateSamplePatients();

                Snackbar?.Add($"Found {TotalPatients} patients", Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error searching patients: {ex.Message}");
                Snackbar?.Add("Error searching patients", Severity.Error);
                PatientResults = new List<PatientRegistryModel>();
            }
            finally
            {
                StateHasChanged();
            }
        }

        private void ClearFilters()
        {
            AgeFrom = null;
            AgeTo = null;
            Sex = string.Empty;
            PatientName = string.Empty;
            PhoneNumber = string.Empty;
            Insurance = string.Empty;
            PatientResults = new List<PatientRegistryModel>();
            SearchPerformed = false;
            StateHasChanged();
            Snackbar?.Add("Filters cleared", Severity.Info);
        }

        private async Task ExportData()
        {
            try
            {
                if (!HasResults)
                {
                    Snackbar?.Add("No data to export", Severity.Warning);
                    return;
                }

                // Simple CSV export simulation
                var csv = "Patient Name,Date of Birth,Sex,Age,Phone Number\n";
                foreach (var patient in PatientResults)
                {
                    csv += $"\"{patient.FullName}\",\"{patient.DateOfBirth?.ToString("MM/dd/yyyy")}\",\"{patient.Sex}\",{patient.Age},\"{patient.PhoneNumber}\"\n";
                }

                // For demo purposes, just show success message
                Snackbar?.Add($"Exported {TotalPatients} patients to CSV", Severity.Success);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error exporting data: {ex.Message}");
                Snackbar?.Add("Error exporting data", Severity.Error);
            }
        }

        private async Task ViewPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient chart (placeholder)
                Snackbar?.Add($"Viewing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error viewing patient: {ex.Message}");
                Snackbar?.Add("Error viewing patient", Severity.Error);
            }
        }

        private async Task EditPatient(Guid patientId)
        {
            try
            {
                // Navigate to patient edit (placeholder)
                Snackbar?.Add($"Editing patient: {patientId}", Severity.Info);
            }
            catch (Exception ex)
            {
                Logger?.LogError($"Error editing patient: {ex.Message}");
                Snackbar?.Add("Error editing patient", Severity.Error);
            }
        }

        private List<PatientRegistryModel> GenerateSamplePatients()
        {
            var patients = new List<PatientRegistryModel>();
            var random = new Random();
            var firstNames = new[] { "John", "Jane", "Michael", "Sarah", "David", "Lisa", "Robert", "Emily", "James", "Ashley" };
            var lastNames = new[] { "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis", "Rodriguez", "Martinez" };
            var sexes = new[] { "Male", "Female" };

            // Generate 5-15 sample patients
            var count = random.Next(5, 16);

            for (int i = 0; i < count; i++)
            {
                var firstName = firstNames[random.Next(firstNames.Length)];
                var lastName = lastNames[random.Next(lastNames.Length)];
                var sex = sexes[random.Next(sexes.Length)];
                var age = random.Next(18, 85);
                var dob = DateTime.Now.AddYears(-age).AddDays(random.Next(-365, 365));

                // Apply filters
                if (!string.IsNullOrEmpty(PatientName) &&
                    !$"{firstName} {lastName}".Contains(PatientName, StringComparison.OrdinalIgnoreCase))
                    continue;

                if (!string.IsNullOrEmpty(Sex) && Sex != "All" && sex != Sex)
                    continue;

                if (AgeFrom.HasValue && age < AgeFrom.Value)
                    continue;

                if (AgeTo.HasValue && age > AgeTo.Value)
                    continue;

                patients.Add(new PatientRegistryModel
                {
                    Id = Guid.NewGuid(),
                    FullName = $"{firstName} {lastName}",
                    DateOfBirth = dob,
                    Sex = sex,
                    Age = age,
                    PhoneNumber = $"({random.Next(100, 999)}) {random.Next(100, 999)}-{random.Next(1000, 9999)}"
                });
            }

            return patients;
        }
    }

    // Simple model for patient registry
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Sex { get; set; } = string.Empty;
        public int Age { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
    }
}
