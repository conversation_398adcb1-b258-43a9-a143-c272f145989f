.datahub-container {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 0;
}

.datahub-container .mud-container {
    max-width: 100% !important;
}

/* Header Styling */
.datahub-container .mud-paper:first-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.datahub-container .mud-paper:first-child .mud-text {
    color: white !important;
}

.datahub-container .mud-paper:first-child .mud-icon {
    color: white !important;
}

/* Filter Section */
.datahub-container .mud-grid .mud-item {
    padding: 8px;
}

.datahub-container .mud-text-field {
    margin-bottom: 8px;
}

.datahub-container .mud-select {
    margin-bottom: 8px;
}

.datahub-container .mud-date-picker {
    margin-bottom: 8px;
}

/* Button Styling */
.datahub-container .mud-button {
    margin-right: 8px;
    margin-bottom: 8px;
    text-transform: none;
    font-weight: 500;
}

.datahub-container .mud-button-filled {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.datahub-container .mud-button-outlined {
    border-width: 1.5px;
}

/* Results Table */
.datahub-container .mud-table {
    border-radius: 8px;
    overflow: hidden;
}

.datahub-container .mud-table-head {
    background-color: #f8f9fa;
}

.datahub-container .mud-table-head .mud-table-cell {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.datahub-container .mud-table-row:hover {
    background-color: #f8f9fa;
    transition: background-color 0.2s ease;
}

.datahub-container .mud-table-cell {
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

/* Action Buttons in Table */
.datahub-container .mud-icon-button {
    transition: all 0.2s ease;
}

.datahub-container .mud-icon-button:hover {
    transform: scale(1.1);
}

/* Pagination */
.datahub-container .mud-pagination {
    justify-content: center;
    margin-top: 16px;
}

.datahub-container .mud-pagination .mud-button {
    margin: 0 2px;
}

/* Loading State */
.datahub-container .mud-progress-linear {
    border-radius: 4px;
    height: 6px;
}

/* Alert Styling */
.datahub-container .mud-alert {
    border-radius: 8px;
    border-left: 4px solid;
}

.datahub-container .mud-alert-info {
    border-left-color: #17a2b8;
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Dialog Styling */
.datahub-container .mud-dialog {
    border-radius: 12px;
}

.datahub-container .mud-dialog-content {
    padding: 24px;
}

.datahub-container .mud-dialog-actions {
    padding: 16px 24px;
    border-top: 1px solid #e9ecef;
}

/* Radio Group Styling */
.datahub-container .mud-radio-group {
    padding: 16px 0;
}

.datahub-container .mud-radio {
    margin-bottom: 8px;
}

/* Paper Elevation Effects */
.datahub-container .mud-paper {
    border-radius: 12px;
    transition: box-shadow 0.3s ease;
}

.datahub-container .mud-paper:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .datahub-container .mud-container {
        padding: 8px;
    }
    
    .datahub-container .mud-paper {
        padding: 16px;
        margin-bottom: 16px;
    }
    
    .datahub-container .mud-button {
        width: 100%;
        margin-bottom: 8px;
        margin-right: 0;
    }
    
    .datahub-container .mud-table {
        font-size: 0.875rem;
    }
    
    .datahub-container .mud-table-cell {
        padding: 8px 12px;
    }
}

@media (max-width: 480px) {
    .datahub-container .mud-text.mud-typography-h4 {
        font-size: 1.5rem;
    }
    
    .datahub-container .mud-text.mud-typography-h6 {
        font-size: 1.125rem;
    }
    
    .datahub-container .mud-grid .mud-item {
        padding: 4px;
    }
}

/* Custom Scrollbar for Table */
.datahub-container .mud-table-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.datahub-container .mud-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.datahub-container .mud-table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.datahub-container .mud-table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Focus States */
.datahub-container .mud-text-field:focus-within,
.datahub-container .mud-select:focus-within,
.datahub-container .mud-date-picker:focus-within {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* Animation for Results */
.datahub-container .mud-table-row {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status Indicators */
.datahub-container .status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.datahub-container .status-active {
    background-color: #28a745;
}

.datahub-container .status-inactive {
    background-color: #dc3545;
}

.datahub-container .status-pending {
    background-color: #ffc107;
}
