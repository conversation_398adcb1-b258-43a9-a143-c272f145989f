﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TeyaUIModels.Model;
using Microsoft.Extensions.Logging;

namespace TeyaUIViewModels.ViewModel
{
    public class PatientService : IPatientService
    {
        private readonly IMemberService _memberService;
        private readonly ILogger<PatientService> _logger;

        public PatientService(IMemberService memberService, ILogger<PatientService> logger)
        {
            _memberService = memberService;
            _logger = logger;
        }

        public Patient PatientData { get; set; }
        public string VisitStatus { get; set; }
        public string VisitType { get; set; }

        public async Task<List<Patient>> SearchPatientsAsync(PatientSearchCriteria criteria)
        {
            try
            {
                // Get all patients for the organization
                var providerPatients = await _memberService.GetPatientsByOrganizationIdAsync(criteria.OrganizationId, false);

                // Convert ProviderPatient to Patient and apply filters
                var patients = providerPatients.Select(pp => new Patient
                {
                    Id = pp.Id,
                    FirstName = ExtractFirstName(pp.Username),
                    LastName = ExtractLastName(pp.Username),
                    SSN = pp.SSN,
                    // Note: Additional patient details would need to be fetched from patient-specific service
                    // For now, using placeholder values
                    DateOfBirth = DateTime.Now.AddYears(-30), // Placeholder
                    Sex = "Unknown", // Placeholder
                    PhoneNumber = "", // Placeholder
                    AccountNumber = pp.SSN?.ToString() ?? ""
                }).ToList();

                // Apply filters
                if (criteria.AgeFrom.HasValue || criteria.AgeTo.HasValue)
                {
                    patients = patients.Where(p =>
                    {
                        var age = CalculateAge(p.DateOfBirth);
                        return (!criteria.AgeFrom.HasValue || age >= criteria.AgeFrom.Value) &&
                               (!criteria.AgeTo.HasValue || age <= criteria.AgeTo.Value);
                    }).ToList();
                }

                if (!string.IsNullOrEmpty(criteria.Sex))
                {
                    patients = patients.Where(p => p.Sex.Equals(criteria.Sex, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                if (criteria.DOBFrom.HasValue)
                {
                    patients = patients.Where(p => p.DateOfBirth >= criteria.DOBFrom.Value).ToList();
                }

                if (criteria.DOBTo.HasValue)
                {
                    patients = patients.Where(p => p.DateOfBirth <= criteria.DOBTo.Value).ToList();
                }

                // Additional filters for PCP, Insurance, Language would require additional data sources
                // For now, returning filtered results based on available data

                return patients;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error searching patients: {ex.Message}");
                return new List<Patient>();
            }
        }

        public async Task<Patient> GetPatientByIdAsync(Guid patientId, Guid organizationId)
        {
            try
            {
                // This would typically call a patient-specific service
                // For now, returning a placeholder implementation
                var providerPatients = await _memberService.GetPatientsByOrganizationIdAsync(organizationId, false);
                var providerPatient = providerPatients.FirstOrDefault(p => p.Id == patientId);

                if (providerPatient != null)
                {
                    return new Patient
                    {
                        Id = providerPatient.Id,
                        FirstName = ExtractFirstName(providerPatient.Username),
                        LastName = ExtractLastName(providerPatient.Username),
                        SSN = providerPatient.SSN
                    };
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting patient by ID: {ex.Message}");
                return null;
            }
        }

        public async Task<List<Patient>> GetPatientsByOrganizationIdAsync(Guid organizationId)
        {
            try
            {
                var providerPatients = await _memberService.GetPatientsByOrganizationIdAsync(organizationId, false);

                return providerPatients.Select(pp => new Patient
                {
                    Id = pp.Id,
                    FirstName = ExtractFirstName(pp.Username),
                    LastName = ExtractLastName(pp.Username),
                    SSN = pp.SSN
                }).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting patients by organization ID: {ex.Message}");
                return new List<Patient>();
            }
        }

        private string ExtractFirstName(string username)
        {
            if (string.IsNullOrEmpty(username)) return "";
            var parts = username.Split(' ');
            return parts.Length > 0 ? parts[0] : "";
        }

        private string ExtractLastName(string username)
        {
            if (string.IsNullOrEmpty(username)) return "";
            var parts = username.Split(' ');
            return parts.Length > 1 ? string.Join(" ", parts.Skip(1)) : "";
        }

        private int CalculateAge(DateTime? dateOfBirth)
        {
            if (!dateOfBirth.HasValue) return 0;

            var today = DateTime.Today;
            var age = today.Year - dateOfBirth.Value.Year;

            if (dateOfBirth.Value.Date > today.AddYears(-age))
                age--;

            return age;
        }
    }
}
