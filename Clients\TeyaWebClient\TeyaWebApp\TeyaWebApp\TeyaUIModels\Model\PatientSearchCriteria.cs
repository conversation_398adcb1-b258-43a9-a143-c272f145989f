using System;

namespace TeyaUIModels.Model
{
    // Search Criteria Model for Patient Registry
    public class PatientSearchCriteria
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string Sex { get; set; } = string.Empty;
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public Guid OrganizationId { get; set; }
    }

    // Filter Model for DataHub UI
    public class DataHubFilterModel
    {
        public int? AgeFrom { get; set; }
        public int? AgeTo { get; set; }
        public string Sex { get; set; } = string.Empty;
        public DateTime? DOBFrom { get; set; }
        public DateTime? DOBTo { get; set; }
        public string PCP { get; set; } = string.Empty;
        public string Insurance { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
    }

    // Patient Registry Model for DataHub display
    public class PatientRegistryModel
    {
        public Guid Id { get; set; }
        public string FullName { get; set; } = string.Empty;
        public DateTime? DateOfBirth { get; set; }
        public string Sex { get; set; } = string.Empty;
        public int Age { get; set; }
        public string PhoneNumber { get; set; } = string.Empty;
        public string AccountNumber { get; set; } = string.Empty;
    }
}
