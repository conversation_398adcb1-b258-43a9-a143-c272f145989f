@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@attribute [Authorize]

<PageTitle>DataHub</PageTitle>

@if (IsLoading)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudProgressCircular Indeterminate="true" />
        <MudText>Loading DataHub...</MudText>
    </MudContainer>
}
else if (HasError)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudAlert Severity="Severity.Error">
            <MudText>Error loading DataHub: @ErrorMessage</MudText>
            <MudButton Color="Color.Primary" OnClick="RefreshPage" Class="mt-2">
                Refresh Page
            </MudButton>
        </MudAlert>
    </MudContainer>
}
else
{
<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                DataHub
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                Patient Registry Management
            </MudText>
        </MudPaper>

        <!-- Simple Test Content -->
        <MudPaper Class="pa-4 mb-4" Elevation="1">
            <MudText Typo="Typo.h6" Class="mb-3">DataHub Test</MudText>
            <MudText>This is a simplified DataHub component to test basic functionality.</MudText>
            <MudText Class="mt-2">Current time: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</MudText>

            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="TestAction" Class="mt-3">
                Test Button
            </MudButton>

            @if (ButtonClicked)
            {
                <MudAlert Severity="Severity.Success" Class="mt-2">
                    Button clicked successfully! DataHub is working.
                </MudAlert>
            }
        </MudPaper>

    </MudContainer>
</div>
}

            <MudGrid>
                <!-- Age Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeFrom"
                                  Label="@Localizer["AgeFrom"]"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeTo"
                                  Label="@Localizer["AgeTo"]"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>

                <!-- Sex -->
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect @bind-Value="FilterModel.Sex"
                               Label="@Localizer["Sex"]"
                               Variant="Variant.Outlined"
                               Margin="Margin.Dense"
                               T="string">
                        <MudSelectItem Value="@("")">@Localizer["All"]</MudSelectItem>
                        <MudSelectItem Value="@("Male")">@Localizer["Male"]</MudSelectItem>
                        <MudSelectItem Value="@("Female")">@Localizer["Female"]</MudSelectItem>
                        <MudSelectItem Value="@("Other")">@Localizer["Other"]</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <!-- DOB Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudDatePicker @bind-Date="FilterModel.DOBFrom"
                                   Label="@Localizer["DOBFrom"]"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudDatePicker @bind-Date="FilterModel.DOBTo"
                                   Label="@Localizer["DOBTo"]"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense" />
                </MudItem>

                <!-- PCP -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.PCP"
                                  Label="@Localizer["PCP"]"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Insurance -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.Insurance"
                                  Label="@Localizer["Insurance"]"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Language -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.Language"
                                  Label="@Localizer["Language"]"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>
            </MudGrid>

            <!-- Advanced Filters Toggle -->
            <MudGrid Class="mt-2">
                <MudItem xs="12">
                    <MudButton Variant="Variant.Text"
                               StartIcon="@(ShowAdvancedFilters ? Icons.Material.Filled.ExpandLess : Icons.Material.Filled.ExpandMore)"
                               OnClick="ToggleAdvancedFilters"
                               Class="mb-2">
                        @Localizer["AdvancedFilters"]
                    </MudButton>
                </MudItem>
            </MudGrid>

            <!-- Advanced Filters Section -->
            <MudCollapse Expanded="ShowAdvancedFilters">
                <MudGrid>
                    <!-- Patient Name -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.PatientName"
                                      Label="@Localizer["PatientName"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Phone Number -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.PhoneNumber"
                                      Label="@Localizer["PhoneNumber"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Account Number -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.AccountNumber"
                                      Label="@Localizer["AccountNumber"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Address -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.Address"
                                      Label="@Localizer["Address"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- City -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.City"
                                      Label="@Localizer["City"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- State -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.State"
                                      Label="@Localizer["State"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Zip Code -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.ZipCode"
                                      Label="@Localizer["ZipCode"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Last Visit Range -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudDatePicker @bind-Date="FilterModel.LastVisitFrom"
                                       Label="@Localizer["LastVisitFrom"]"
                                       Variant="Variant.Outlined"
                                       Margin="Margin.Dense" />
                    </MudItem>
                    <MudItem xs="12" sm="6" md="3">
                        <MudDatePicker @bind-Date="FilterModel.LastVisitTo"
                                       Label="@Localizer["LastVisitTo"]"
                                       Variant="Variant.Outlined"
                                       Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Risk Level -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudSelect @bind-Value="FilterModel.RiskLevel"
                                   Label="@Localizer["RiskLevel"]"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense"
                                   T="string">
                            <MudSelectItem Value="@("")">@Localizer["All"]</MudSelectItem>
                            <MudSelectItem Value="@("Low")">@Localizer["Low"]</MudSelectItem>
                            <MudSelectItem Value="@("Medium")">@Localizer["Medium"]</MudSelectItem>
                            <MudSelectItem Value="@("High")">@Localizer["High"]</MudSelectItem>
                            <MudSelectItem Value="@("Critical")">@Localizer["Critical"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>

                    <!-- Active Status -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudSelect @bind-Value="FilterModel.IsActive"
                                   Label="@Localizer["ActiveStatus"]"
                                   Variant="Variant.Outlined"
                                   Margin="Margin.Dense"
                                   T="bool?">
                            <MudSelectItem Value="@((bool?)null)">@Localizer["All"]</MudSelectItem>
                            <MudSelectItem Value="@(true)">@Localizer["Active"]</MudSelectItem>
                            <MudSelectItem Value="@(false)">@Localizer["Inactive"]</MudSelectItem>
                        </MudSelect>
                    </MudItem>

                    <!-- Diagnosis -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.Diagnosis"
                                      Label="@Localizer["Diagnosis"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>

                    <!-- Medication -->
                    <MudItem xs="12" sm="6" md="3">
                        <MudTextField @bind-Value="FilterModel.Medication"
                                      Label="@Localizer["Medication"]"
                                      Variant="Variant.Outlined"
                                      Margin="Margin.Dense" />
                    </MudItem>
                </MudGrid>
            </MudCollapse>

            <!-- Action Buttons -->
            <MudGrid Class="mt-4">
                <MudItem xs="12">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Search"
                               OnClick="SearchPatients"
                               Class="mr-2">
                        @Localizer["Search"]
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Clear"
                               OnClick="ClearFilters"
                               Class="mr-2">
                        @Localizer["Clear"]
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Info"
                               StartIcon="@Icons.Material.Filled.Save"
                               OnClick="SaveQuery"
                               Class="mr-2">
                        @Localizer["SaveQuery"]
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Success"
                               StartIcon="@Icons.Material.Filled.PlayArrow"
                               OnClick="RunSubset"
                               Class="mr-2">
                        @Localizer["RunSubset"]
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Warning"
                               StartIcon="@Icons.Material.Filled.Download"
                               OnClick="ExportData"
                               Disabled="@(!HasResults)">
                        @Localizer["Export"]
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- Results Section -->
        @if (IsLoading)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudProgressLinear Indeterminate="true" />
                <MudText Align="Align.Center" Class="mt-2">@Localizer["LoadingPatients"]</MudText>
            </MudPaper>
        }
        else if (HasResults)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudText Typo="Typo.h6" Class="mb-3">
                    @Localizer["SearchResults"] (@TotalPatients @Localizer["Patients"])
                </MudText>

                <MudTable Items="@PatientResults"
                          Hover="true"
                          Striped="true"
                          Dense="true"
                          FixedHeader="true"
                          Height="400px">
                    <HeaderContent>
                        <MudTh>@Localizer["PatientName"]</MudTh>
                        <MudTh>@Localizer["DOB"]</MudTh>
                        <MudTh>@Localizer["Sex"]</MudTh>
                        <MudTh>@Localizer["Age"]</MudTh>
                        <MudTh>@Localizer["PhoneNumber"]</MudTh>
                        <MudTh>@Localizer["ACC"]</MudTh>
                        <MudTh>@Localizer["Address"]</MudTh>
                        <MudTh>@Localizer["PCP"]</MudTh>
                        <MudTh>@Localizer["Insurance"]</MudTh>
                        <MudTh>@Localizer["LastVisit"]</MudTh>
                        <MudTh>@Localizer["RiskLevel"]</MudTh>
                        <MudTh>@Localizer["Status"]</MudTh>
                        <MudTh>@Localizer["Actions"]</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="@Localizer["PatientName"]">
                            <div class="d-flex align-center">
                                <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-2">
                                    @context.FullName.Substring(0, Math.Min(2, context.FullName.Length)).ToUpper()
                                </MudAvatar>
                                @context.FullName
                            </div>
                        </MudTd>
                        <MudTd DataLabel="@Localizer["DOB"]">@context.DateOfBirth?.ToString("MM/dd/yyyy")</MudTd>
                        <MudTd DataLabel="@Localizer["Sex"]">@context.Sex</MudTd>
                        <MudTd DataLabel="@Localizer["Age"]">@context.Age</MudTd>
                        <MudTd DataLabel="@Localizer["PhoneNumber"]">@context.PhoneNumber</MudTd>
                        <MudTd DataLabel="@Localizer["ACC"]">@context.AccountNumber</MudTd>
                        <MudTd DataLabel="@Localizer["Address"]">
                            @if (!string.IsNullOrEmpty(context.Address))
                            {
                                <span>@context.Address</span>
                                @if (!string.IsNullOrEmpty(context.City))
                                {
                                    <br /><small class="text-muted">@context.City, @context.State @context.ZipCode</small>
                                }
                            }
                        </MudTd>
                        <MudTd DataLabel="@Localizer["PCP"]">@context.PCP</MudTd>
                        <MudTd DataLabel="@Localizer["Insurance"]">@context.Insurance</MudTd>
                        <MudTd DataLabel="@Localizer["LastVisit"]">@context.LastVisit?.ToString("MM/dd/yyyy")</MudTd>
                        <MudTd DataLabel="@Localizer["RiskLevel"]">
                            @if (!string.IsNullOrEmpty(context.RiskLevel))
                            {
                                <MudChip T="string" Color="@GetRiskLevelColor(context.RiskLevel)" Size="Size.Small">
                                    @context.RiskLevel
                                </MudChip>
                            }
                        </MudTd>
                        <MudTd DataLabel="@Localizer["Status"]">
                            <MudChip T="string" Color="@(context.IsActive ? Color.Success : Color.Error)" Size="Size.Small">
                                @(context.IsActive ? Localizer["Active"] : Localizer["Inactive"])
                            </MudChip>
                        </MudTd>
                        <MudTd DataLabel="@Localizer["Actions"]">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                           Color="Color.Primary"
                                           Size="Size.Small"
                                           OnClick="@(() => ViewPatient(context.Id))"
                                           Variant="Variant.Text" />
                            <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                           Color="Color.Secondary"
                                           Size="Size.Small"
                                           OnClick="@(() => EditPatient(context.Id))"
                                           Variant="Variant.Text" />
                        </MudTd>
                    </RowTemplate>
                </MudTable>

                <!-- Pagination -->
                <MudPagination Count="@TotalPages"
                               Selected="@CurrentPage"
                               SelectedChanged="@OnPageChanged"
                               Class="mt-4" />
            </MudPaper>
        }
        else if (SearchPerformed && !HasResults)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    @Localizer["NoResultsFound"]
                </MudAlert>
            </MudPaper>
        }
    </MudContainer>
</div>

<!-- Save Query Dialog -->
<MudDialog @bind-Visible="ShowSaveQueryDialog" Options="@(new DialogOptions { MaxWidth = MaxWidth.Small })">
    <DialogContent>
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["SaveQuery"]</MudText>
        <MudTextField @bind-Value="QueryName"
                      Label="@Localizer["QueryName"]"
                      Variant="Variant.Outlined"
                      FullWidth="true" />
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => ShowSaveQueryDialog = false)">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveQueryConfirm">@Localizer["Save"]</MudButton>
    </DialogActions>
</MudDialog>

<!-- Export Options Dialog -->
<MudDialog @bind-Visible="ShowExportDialog" Options="@(new DialogOptions { MaxWidth = MaxWidth.Small })">
    <DialogContent>
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["ExportOptions"]</MudText>
        <MudRadioGroup T="string" @bind-Value="ExportFormat">
            <MudRadio T="string" Value="@("CSV")" Color="Color.Primary">CSV</MudRadio>
            <MudRadio T="string" Value="@("Excel")" Color="Color.Primary">Excel</MudRadio>
            <MudRadio T="string" Value="@("PDF")" Color="Color.Primary">PDF</MudRadio>
        </MudRadioGroup>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => ShowExportDialog = false)">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="ExportConfirm">@Localizer["Export"]</MudButton>
    </DialogActions>
</MudDialog>
}

<script>
    window.downloadFile = function (fileName, base64Content, contentType) {
        const linkSource = `data:${contentType};base64,${base64Content}`;
        const downloadLink = document.createElement("a");
        downloadLink.href = linkSource;
        downloadLink.download = fileName;
        downloadLink.click();
    };
</script>
