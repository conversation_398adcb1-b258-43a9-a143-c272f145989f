@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using Microsoft.Extensions.Localization
@using MudBlazor
@using TeyaWebApp.Models
@using TeyaWebApp.Services
@using TeyaUIModels.Model
@using TeyaUIModels.ViewModel
@using System.ComponentModel.DataAnnotations
@attribute [Authorize]

<PageTitle>@Localizer["DataHub"]</PageTitle>

<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                @Localizer["DataHub"]
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                @Localizer["PatientRegistryManagement"]
            </MudText>
        </MudPaper>

        <!-- Filter Section -->
        <MudPaper Class="pa-4 mb-4" Elevation="1">
            <MudText Typo="Typo.h6" Class="mb-3">@Localizer["Filters"]</MudText>
            
            <MudGrid>
                <!-- Age Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeFrom" 
                                  Label="@Localizer["AgeFrom"]" 
                                  Variant="Variant.Outlined" 
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.AgeTo" 
                                  Label="@Localizer["AgeTo"]" 
                                  Variant="Variant.Outlined" 
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>

                <!-- Sex -->
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect @bind-Value="FilterModel.Sex" 
                               Label="@Localizer["Sex"]" 
                               Variant="Variant.Outlined" 
                               Margin="Margin.Dense"
                               T="string">
                        <MudSelectItem Value="@("")">@Localizer["All"]</MudSelectItem>
                        <MudSelectItem Value="@("Male")">@Localizer["Male"]</MudSelectItem>
                        <MudSelectItem Value="@("Female")">@Localizer["Female"]</MudSelectItem>
                        <MudSelectItem Value="@("Other")">@Localizer["Other"]</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <!-- DOB Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudDatePicker @bind-Date="FilterModel.DOBFrom" 
                                   Label="@Localizer["DOBFrom"]" 
                                   Variant="Variant.Outlined" 
                                   Margin="Margin.Dense" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudDatePicker @bind-Date="FilterModel.DOBTo" 
                                   Label="@Localizer["DOBTo"]" 
                                   Variant="Variant.Outlined" 
                                   Margin="Margin.Dense" />
                </MudItem>

                <!-- PCP -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.PCP" 
                                  Label="@Localizer["PCP"]" 
                                  Variant="Variant.Outlined" 
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Insurance -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.Insurance" 
                                  Label="@Localizer["Insurance"]" 
                                  Variant="Variant.Outlined" 
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Language -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="FilterModel.Language" 
                                  Label="@Localizer["Language"]" 
                                  Variant="Variant.Outlined" 
                                  Margin="Margin.Dense" />
                </MudItem>
            </MudGrid>

            <!-- Action Buttons -->
            <MudGrid Class="mt-4">
                <MudItem xs="12">
                    <MudButton Variant="Variant.Filled" 
                               Color="Color.Primary" 
                               StartIcon="@Icons.Material.Filled.Search"
                               OnClick="SearchPatients"
                               Class="mr-2">
                        @Localizer["Search"]
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Secondary" 
                               StartIcon="@Icons.Material.Filled.Clear"
                               OnClick="ClearFilters"
                               Class="mr-2">
                        @Localizer["Clear"]
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Info" 
                               StartIcon="@Icons.Material.Filled.Save"
                               OnClick="SaveQuery"
                               Class="mr-2">
                        @Localizer["SaveQuery"]
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Success" 
                               StartIcon="@Icons.Material.Filled.PlayArrow"
                               OnClick="RunSubset"
                               Class="mr-2">
                        @Localizer["RunSubset"]
                    </MudButton>
                    
                    <MudButton Variant="Variant.Outlined" 
                               Color="Color.Warning" 
                               StartIcon="@Icons.Material.Filled.Download"
                               OnClick="ExportData"
                               Disabled="@(!HasResults)">
                        @Localizer["Export"]
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- Results Section -->
        @if (IsLoading)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudProgressLinear Indeterminate="true" />
                <MudText Align="Align.Center" Class="mt-2">@Localizer["LoadingPatients"]</MudText>
            </MudPaper>
        }
        else if (HasResults)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudText Typo="Typo.h6" Class="mb-3">
                    @Localizer["SearchResults"] (@TotalPatients @Localizer["Patients"])
                </MudText>
                
                <MudTable Items="@PatientResults" 
                          Hover="true" 
                          Striped="true" 
                          Dense="true"
                          FixedHeader="true"
                          Height="400px">
                    <HeaderContent>
                        <MudTh>@Localizer["PatientName"]</MudTh>
                        <MudTh>@Localizer["DOB"]</MudTh>
                        <MudTh>@Localizer["Sex"]</MudTh>
                        <MudTh>@Localizer["Age"]</MudTh>
                        <MudTh>@Localizer["TelNo"]</MudTh>
                        <MudTh>@Localizer["ACC"]</MudTh>
                        <MudTh>@Localizer["Actions"]</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="@Localizer["PatientName"]">@context.FullName</MudTd>
                        <MudTd DataLabel="@Localizer["DOB"]">@context.DateOfBirth?.ToString("MM/dd/yyyy")</MudTd>
                        <MudTd DataLabel="@Localizer["Sex"]">@context.Sex</MudTd>
                        <MudTd DataLabel="@Localizer["Age"]">@context.Age</MudTd>
                        <MudTd DataLabel="@Localizer["TelNo"]">@context.PhoneNumber</MudTd>
                        <MudTd DataLabel="@Localizer["ACC"]">@context.AccountNumber</MudTd>
                        <MudTd DataLabel="@Localizer["Actions"]">
                            <MudIconButton Icon="@Icons.Material.Filled.Visibility" 
                                           Color="Color.Primary" 
                                           Size="Size.Small"
                                           OnClick="@(() => ViewPatient(context.Id))" />
                        </MudTd>
                    </RowTemplate>
                </MudTable>

                <!-- Pagination -->
                <MudPagination Count="@TotalPages" 
                               Selected="@CurrentPage" 
                               SelectedChanged="@OnPageChanged"
                               Class="mt-4" />
            </MudPaper>
        }
        else if (SearchPerformed && !HasResults)
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    @Localizer["NoResultsFound"]
                </MudAlert>
            </MudPaper>
        }
    </MudContainer>
</div>

<!-- Save Query Dialog -->
<MudDialog @bind-IsVisible="ShowSaveQueryDialog" Options="@(new DialogOptions { MaxWidth = MaxWidth.Small })">
    <DialogContent>
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["SaveQuery"]</MudText>
        <MudTextField @bind-Value="QueryName" 
                      Label="@Localizer["QueryName"]" 
                      Variant="Variant.Outlined" 
                      FullWidth="true" />
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => ShowSaveQueryDialog = false)">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="SaveQueryConfirm">@Localizer["Save"]</MudButton>
    </DialogActions>
</MudDialog>

<!-- Export Options Dialog -->
<MudDialog @bind-IsVisible="ShowExportDialog" Options="@(new DialogOptions { MaxWidth = MaxWidth.Small })">
    <DialogContent>
        <MudText Typo="Typo.h6" Class="mb-4">@Localizer["ExportOptions"]</MudText>
        <MudRadioGroup @bind-SelectedOption="ExportFormat">
            <MudRadio Option="@("CSV")" Color="Color.Primary">CSV</MudRadio>
            <MudRadio Option="@("Excel")" Color="Color.Primary">Excel</MudRadio>
            <MudRadio Option="@("PDF")" Color="Color.Primary">PDF</MudRadio>
        </MudRadioGroup>
    </DialogContent>
    <DialogActions>
        <MudButton OnClick="@(() => ShowExportDialog = false)">@Localizer["Cancel"]</MudButton>
        <MudButton Color="Color.Primary" Variant="Variant.Filled" OnClick="ExportConfirm">@Localizer["Export"]</MudButton>
    </DialogActions>
</MudDialog>
