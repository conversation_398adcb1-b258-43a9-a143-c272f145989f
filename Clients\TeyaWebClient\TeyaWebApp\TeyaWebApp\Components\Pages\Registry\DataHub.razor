@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@attribute [Authorize]

<PageTitle>DataHub</PageTitle>

@if (IsLoading)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudProgressCircular Indeterminate="true" />
        <MudText>Loading DataHub...</MudText>
    </MudContainer>
}
else if (HasError)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudAlert Severity="Severity.Error">
            <MudText>Error loading DataHub: @ErrorMessage</MudText>
            <MudButton Color="Color.Primary" OnClick="RefreshPage" Class="mt-2">
                Refresh Page
            </MudButton>
        </MudAlert>
    </MudContainer>
}
else
{
<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                DataHub
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                Patient Registry Management
            </MudText>
        </MudPaper>

        <!-- Filter Section -->
        <MudPaper Class="pa-4 mb-4" Elevation="1">
            <MudText Typo="Typo.h6" Class="mb-3">Patient Search Filters</MudText>

            <MudGrid>
                <!-- Age Range -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="AgeFrom"
                                  Label="Age From"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="AgeTo"
                                  Label="Age To"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense"
                                  T="int?" />
                </MudItem>

                <!-- Sex -->
                <MudItem xs="12" sm="6" md="3">
                    <MudSelect @bind-Value="Sex"
                               Label="Sex"
                               Variant="Variant.Outlined"
                               Margin="Margin.Dense"
                               T="string">
                        <MudSelectItem Value="@("")">All</MudSelectItem>
                        <MudSelectItem Value="@("Male")">Male</MudSelectItem>
                        <MudSelectItem Value="@("Female")">Female</MudSelectItem>
                        <MudSelectItem Value="@("Other")">Other</MudSelectItem>
                    </MudSelect>
                </MudItem>

                <!-- Patient Name -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="PatientName"
                                  Label="Patient Name"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Phone Number -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="PhoneNumber"
                                  Label="Phone Number"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>

                <!-- Insurance -->
                <MudItem xs="12" sm="6" md="3">
                    <MudTextField @bind-Value="Insurance"
                                  Label="Insurance"
                                  Variant="Variant.Outlined"
                                  Margin="Margin.Dense" />
                </MudItem>
            </MudGrid>

            <!-- Action Buttons -->
            <MudGrid Class="mt-4">
                <MudItem xs="12">
                    <MudButton Variant="Variant.Filled"
                               Color="Color.Primary"
                               StartIcon="@Icons.Material.Filled.Search"
                               OnClick="SearchPatients"
                               Class="mr-2">
                        Search Patients
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Secondary"
                               StartIcon="@Icons.Material.Filled.Clear"
                               OnClick="ClearFilters"
                               Class="mr-2">
                        Clear Filters
                    </MudButton>

                    <MudButton Variant="Variant.Outlined"
                               Color="Color.Warning"
                               StartIcon="@Icons.Material.Filled.Download"
                               OnClick="ExportData"
                               Disabled="@(!HasResults)">
                        Export Data
                    </MudButton>
                </MudItem>
            </MudGrid>
        </MudPaper>

        <!-- Results Section -->
        @if (SearchPerformed)
        {
            @if (HasResults)
            {
                <MudPaper Class="pa-4" Elevation="1">
                    <MudText Typo="Typo.h6" Class="mb-3">
                        Search Results (@TotalPatients Patients)
                    </MudText>

                    <MudTable Items="@PatientResults"
                              Hover="true"
                              Striped="true"
                              Dense="true"
                              FixedHeader="true"
                              Height="400px">
                        <HeaderContent>
                            <MudTh>Patient Name</MudTh>
                            <MudTh>Date of Birth</MudTh>
                            <MudTh>Sex</MudTh>
                            <MudTh>Age</MudTh>
                            <MudTh>Phone Number</MudTh>
                            <MudTh>Actions</MudTh>
                        </HeaderContent>
                        <RowTemplate>
                            <MudTd DataLabel="Patient Name">
                                <div class="d-flex align-center">
                                    <MudAvatar Color="Color.Primary" Size="Size.Small" Class="mr-2">
                                        @context.FullName.Substring(0, Math.Min(2, context.FullName.Length)).ToUpper()
                                    </MudAvatar>
                                    @context.FullName
                                </div>
                            </MudTd>
                            <MudTd DataLabel="Date of Birth">@context.DateOfBirth?.ToString("MM/dd/yyyy")</MudTd>
                            <MudTd DataLabel="Sex">@context.Sex</MudTd>
                            <MudTd DataLabel="Age">@context.Age</MudTd>
                            <MudTd DataLabel="Phone Number">@context.PhoneNumber</MudTd>
                            <MudTd DataLabel="Actions">
                                <MudIconButton Icon="@Icons.Material.Filled.Visibility"
                                               Color="Color.Primary"
                                               Size="Size.Small"
                                               OnClick="@(() => ViewPatient(context.Id))"
                                               Variant="Variant.Text" />
                                <MudIconButton Icon="@Icons.Material.Filled.Edit"
                                               Color="Color.Secondary"
                                               Size="Size.Small"
                                               OnClick="@(() => EditPatient(context.Id))"
                                               Variant="Variant.Text" />
                            </MudTd>
                        </RowTemplate>
                    </MudTable>
                </MudPaper>
            }
            else
            {
                <MudPaper Class="pa-4" Elevation="1">
                    <MudAlert Severity="Severity.Info">
                        No patients found matching your search criteria.
                    </MudAlert>
                </MudPaper>
            }
        }
        else
        {
            <MudPaper Class="pa-4" Elevation="1">
                <MudAlert Severity="Severity.Info">
                    <MudText>Welcome to DataHub - Patient Registry Management</MudText>
                    <MudText Class="mt-2">Use the filters above to search for patients in your organization.</MudText>
                </MudAlert>
            </MudPaper>
        }

    </MudContainer>
</div>
}
