@page "/Registry/DataHub"
@using Microsoft.AspNetCore.Authorization
@using MudBlazor
@using TeyaWebApp.Components.Layout
@layout Admin
@attribute [Authorize]

<PageTitle>DataHub</PageTitle>

@if (IsLoading)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudProgressCircular Indeterminate="true" />
        <MudText>Loading DataHub...</MudText>
    </MudContainer>
}
else if (HasError)
{
    <MudContainer MaxWidth="MaxWidth.False" Class="pa-4">
        <MudAlert Severity="Severity.Error">
            <MudText>Error loading DataHub: @ErrorMessage</MudText>
            <MudButton Color="Color.Primary" OnClick="RefreshPage" Class="mt-2">
                Refresh Page
            </MudButton>
        </MudAlert>
    </MudContainer>
}
else
{
<div class="datahub-container">
    <MudContainer MaxWidth="MaxWidth.ExtraExtraLarge" Class="pa-4">
        <!-- Header -->
        <MudPaper Class="pa-4 mb-4" Elevation="2">
            <MudText Typo="Typo.h4" Class="mb-2">
                <MudIcon Icon="@Icons.Material.Filled.Dashboard" Class="mr-2" />
                DataHub
            </MudText>
            <MudText Typo="Typo.body1" Color="Color.Secondary">
                Patient Registry Management
            </MudText>
        </MudPaper>

        <!-- Simple Test Content -->
        <MudPaper Class="pa-4 mb-4" Elevation="1">
            <MudText Typo="Typo.h6" Class="mb-3">DataHub Test</MudText>
            <MudText>This is a simplified DataHub component to test basic functionality.</MudText>
            <MudText Class="mt-2">Current time: @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</MudText>

            <MudButton Variant="Variant.Filled" Color="Color.Primary" OnClick="TestAction" Class="mt-3">
                Test Button
            </MudButton>

            @if (ButtonClicked)
            {
                <MudAlert Severity="Severity.Success" Class="mt-2">
                    Button clicked successfully! DataHub is working.
                </MudAlert>
            }
        </MudPaper>

    </MudContainer>
</div>
}
